#!/usr/bin/env bash

set -e

PID="0xc30ae712c45d4e0fcdba5190d50ffd52fefde532fa64df5ded2e197e39d80e78"
WAITROOM_OID="0xcb26955fd2eb741ec75a061d8c7da4107e16f97fc609e1045cda31db3569a364"

#  module>::<method>
MODULE_METHOD=$1
# wizard public key
WIZARD_ADDRESS=$2

if [ -z "$MODULE_METHOD" ]; then
  echo "Usage: $0 <MODULE_METHOD: no value> <WIZARD_ADDRESS: no value>"
  exit 1
fi

if [ -z "$WIZARD_ADDRESS" ]; then
  echo "Usage: $0 <MODULE_METHOD:$1> <WIZARD_ADDRESS: no value>"
  exit 1
fi

echo "network: devnet"
echo "package id: $PID"
echo "waitroom oid: $WAITROOM_OID"

PREV_ACTIVE_ADDRESS=$(sui client active-address)

sui client switch --address $WIZARD_ADDRESS


if [ $MODULE_METHOD = "faucet" ]; then
  # NOTE: you must have httpie CLI installed, implicit dependency
  http POST https://faucet.devnet.sui.io/v2/gas FixedAmountRequest:="{\"recipient\":\"$WIZARD_ADDRESS\"}"
fi

if [ $MODULE_METHOD = "duel::setup_wizard" ]; then
  TARGET="$PID::duel::setup_wizard"

  sui client ptb \
    --move-call $TARGET \
    --gas-budget 1000000000
fi

if [ $MODULE_METHOD = "waitroom::join" ]; then
  TARGET="$PID::waitroom::join"

  sui client ptb \
    --move-call $TARGET @$WAITROOM_OID \
    --gas-budget 1000000000
fi

sui client switch --address $PREV_ACTIVE_ADDRESS
