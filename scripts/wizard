#!/usr/bin/env bash

set -e

PID="0x497acf880e1c88f50b1b3c64f2ade9dd2c302ab51e30287ace508a6ac33c3626"
WAITROOM_OID="0x9f97cfd34fb6a63b609c8ef0d151bcc6811181c0333838c3e0f281801adec224"

#  module>::<method>
MODULE_METHOD=$1
# wizard public key
WIZARD_ADDRESS=$2

if [ -z "$MODULE_METHOD" ]; then
  echo "Usage: $0 <MODULE_METHOD: no value> <WIZARD_ADDRESS: no value>"
  exit 1
fi

if [ -z "$WIZARD_ADDRESS" ]; then
  echo "Usage: $0 <MODULE_METHOD:$1> <WIZARD_ADDRESS: no value>"
  exit 1
fi

echo "network: devnet"
echo "package id: $PID"
echo "waitroom oid: $WAITROOM_OID"

PREV_ACTIVE_ADDRESS=$(sui client active-address)

sui client switch --address $WIZARD_ADDRESS


if [ $MODULE_METHOD = "faucet" ]; then
  # NOTE: you must have httpie CLI installed, implicit dependency
  http POST https://faucet.devnet.sui.io/v2/gas FixedAmountRequest:="{\"recipient\":\"$WIZARD_ADDRESS\"}"
fi

if [ $MODULE_METHOD = "duel::setup_wizard" ]; then
  TARGET="$PID::duel::setup_wizard"

  sui client ptb \
    --move-call $TARGET \
    --gas-budget 1000000000
fi

if [ $MODULE_METHOD = "spell::cast_arrow" ]; then
  DUEL_OID=$3
  DUELIST_CAP_OID=$4
  TARGET_ADDRESS=$5
  if [ -z "$DUEL_OID" ]; then
    echo "Usage: $0 <MODULE_METHOD:$1> <WIZARD_ADDRESS:$2> <DUEL_OID: no value> <DUELIST_CAP_OID: no value> <TARGET_ADDRESS: no value>"
    exit 1
  fi
  if [ -z "$DUELIST_CAP_OID" ]; then
    echo "Usage: $0 <MODULE_METHOD:$1> <WIZARD_ADDRESS:$2> <DUEL_OID:$3> <DUELIST_CAP_OID: no value> <TARGET_ADDRESS: no value>"
    exit 1
  fi
  if [ -z "$TARGET_ADDRESS" ]; then
    echo "Usage: $0 <MODULE_METHOD:$1> <WIZARD_ADDRESS:$2> <DUEL_OID:$3> <DUELIST_CAP_OID:$4> <TARGET_ADDRESS: no value>"
    exit 1
  fi

  sui client ptb \
    --move-call "$PID::duel::use_force" @$DUEL_OID @$DUELIST_CAP_OID 4 \
    --assign force \
    --move-call "${PID}::spell::cast_arrow" force \
    --assign damage \
    --move-call "${PID}::damage::apply" damage @$DUEL_OID @$TARGET_ADDRESS \
    --gas-budget 1000000000
fi

if [ $MODULE_METHOD = "waitroom::join" ]; then
  TARGET="$PID::waitroom::join"

  sui client ptb \
    --move-call $TARGET @$WAITROOM_OID \
    --gas-budget 1000000000
fi

sui client switch --address $PREV_ACTIVE_ADDRESS
