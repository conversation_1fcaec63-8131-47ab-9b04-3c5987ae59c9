{"name": "game-chat", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:verify": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@grpc/grpc-js": "^1.13.3", "@grpc/proto-loader": "^0.7.15", "@mysten/dapp-kit": "0.16.2", "@mysten/enoki": "^0.6.0", "@mysten/sui": "1.29.1", "@radix-ui/react-slot": "^1.2.0", "@supabase/supabase-js": "^2.49.4", "@tailwindcss/vite": "^4.1.4", "@tanstack/react-query": "^5.74.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.503.0", "next-themes": "^0.4.6", "react": "^18.3.1", "react-dom": "^18.3.1", "react-router-dom": "^6.20.1", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^4.1.4", "tw-animate-css": "^1.2.8"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^22.14.1", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}