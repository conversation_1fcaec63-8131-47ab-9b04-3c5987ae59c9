import { LoginMenu } from '@/auth/LoginButton'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/context/AuthContext'
import { appUrl } from '@/lib/utils'
import { useCallback } from 'react'

export function Signing() {
  const { signInWithGoogle } = useAuth()

  const handleGoogleSignIn = useCallback(async () => {
    try {
      await signInWithGoogle(appUrl('/d'))
    } catch (error) {
      console.error('Error signing in with Google:', error)
    }
  }, [signInWithGoogle])

  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <div className="flex flex-col items-center gap-4">
        <h1 className="text-2xl font-bold mb-4">Sign in to Magic Duel</h1>

        {/* Enoki zkLogin */}
        <div className="mb-4">
          <h2 className="text-lg font-semibold mb-2">Sign in with zkLogin</h2>
          <LoginMenu redirectOnLgoin="/d" />
        </div>

        {/* Supabase Auth */}
        <div>
          <h2 className="text-lg font-semibold mb-2">Sign in with Supabase</h2>
          <Button onClick={handleGoogleSignIn} className="w-full">
            Sign in with Google
          </Button>
        </div>
      </div>
    </div>
  )
}
