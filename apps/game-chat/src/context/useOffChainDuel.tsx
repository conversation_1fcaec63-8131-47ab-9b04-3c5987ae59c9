import { Duel, DuelState } from "@/lib/duel/Duel"
import { createContext, useContext } from "react"

export type PracticeDuelState =
  /** duel created but not yet started or start time is not yet set */
  | 'pending'
  /** duel in action and spell can be casted */
  | 'started'
  /** duel has a winner */
  | 'finished'
  /** duel context is not loaded yet */
  | 'loading'

export type OffChainDuelContextValue = {
  duel: Duel
  duelState: PracticeDuelState
  duelData: DuelState
  startDuel: (
    args: { countdownSeconds: number },
    opts?: {
      onSuccess?: () => void
      onError?: (error: unknown) => void
      onSettled?: () => void
    }
  ) => void
  castSpell: (
    spellName: string,
    targetType: 'self' | 'opponent',
    opts?: {
      onSuccess?: () => void
      onError?: (error: unknown) => void
      onSettled?: () => void
    }
  ) => void
  winner: string | null
  loser: string | null
  isLoading: boolean
  currentWizardId: string
  opponentId: string
}

const defaultContextValue: OffChainDuelContextValue = {
  duel: new Duel('practice', 'player', 'opponent', false),
  duelState: 'loading',
  duelData: {
    id: 'practice',
    wizard1: { id: 'player', force: 128, effects: [0, 0, 0] },
    wizard2: { id: 'opponent', force: 128, effects: [0, 0, 0] },
    startedAt: 0,
    isOnChain: false,
  },
  startDuel: () => {},
  castSpell: () => {},
  winner: null,
  loser: null,
  isLoading: true,
  currentWizardId: 'player',
  opponentId: 'opponent',
}

export const OffChainDuelContext = createContext<OffChainDuelContextValue>(defaultContextValue)

export function useOffChainDuel() {
  const context = useContext(OffChainDuelContext)
  if (!context) {
    throw new Error('useOffChainDuel must be used within an OffChainDuelProvider')
  }
  return context
}
