import { createContext, useContext, useEffect, useState, ReactNode } from 'react'
import { Session, User } from '@supabase/supabase-js'
import { useCurrentAccount } from '@mysten/dapp-kit'
import { isEnokiWallet } from '@mysten/enoki'
import { Ed25519PublicKey } from '@mysten/sui/keypairs/ed25519'
import { PublicKey } from '@mysten/sui/cryptography'
import { displayName } from '@/lib/user'
import * as supabaseAuth from '@/lib/supabase/auth'
import { UserAccount } from '@/components/Authenticated'

interface AuthContextType {
  session: Session | null
  user: User | null
  userAccount: UserAccount | null
  isLoading: boolean
  signInWithGoogle: (redirectTo?: string) => Promise<void>
  signOut: () => Promise<void>
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [session, setSession] = useState<Session | null>(null)
  const [user, setUser] = useState<User | null>(null)
  const [userAccount, setUserAccount] = useState<UserAccount | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  
  const account = useCurrentAccount()
  const address = account?.address
  const publicKey = account?.publicKey ? account.publicKey.slice(0, 32) : null
  const isZkLogin = account?.wallet ? isEnokiWallet(account.wallet) : false

  // Initialize auth state
  useEffect(() => {
    const initAuth = async () => {
      try {
        // Get the current session
        const currentSession = await supabaseAuth.getSession()
        setSession(currentSession)
        
        if (currentSession) {
          // Get the current user
          const currentUser = await supabaseAuth.getCurrentUser()
          setUser(currentUser)
        }
      } catch (error) {
        console.error('Error initializing auth:', error)
      } finally {
        setIsLoading(false)
      }
    }
    
    initAuth()
    
    // Set up auth state change listener
    const unsubscribe = supabaseAuth.onAuthStateChange((event, newSession) => {
      setSession(newSession)
      
      if (newSession) {
        supabaseAuth.getCurrentUser().then(setUser)
      } else {
        setUser(null)
      }
    })
    
    return () => {
      unsubscribe()
    }
  }, [])
  
  // Update userAccount when account or user changes
  useEffect(() => {
    if (!address) {
      setUserAccount(null)
      return
    }
    
    // For zkLogin accounts
    if (isZkLogin) {
      const zkLoginAccount: UserAccount = {
        id: address,
        username: address,
        displayName: displayName(address),
        // For zkLogin, we use a placeholder PublicKey since we don't need the actual key
        // The actual signing is handled by the wallet adapter
        publicKey: publicKey ? new Ed25519PublicKey(publicKey) : ({} as PublicKey),
        isZkLogin: true,
      }
      
      setUserAccount(zkLoginAccount)
      
      // If we have a Supabase user, update the metadata
      if (user) {
        supabaseAuth.updateUserMetadata(user.id, {
          sui_address: address,
          is_zk_login: true,
          display_name: displayName(address),
        }).catch(error => {
          console.error('Error updating user metadata:', error)
        })
      }
      
      return
    }
    
    // For regular wallets
    if (!publicKey) {
      setUserAccount(null)
      return
    }
    
    const regularAccount: UserAccount = {
      id: address,
      username: address,
      displayName: displayName(address),
      publicKey: new Ed25519PublicKey(publicKey),
      isZkLogin: false,
    }
    
    setUserAccount(regularAccount)
    
    // If we have a Supabase user, update the metadata
    if (user) {
      supabaseAuth.updateUserMetadata(user.id, {
        sui_address: address,
        is_zk_login: false,
        display_name: displayName(address),
      }).catch(error => {
        console.error('Error updating user metadata:', error)
      })
    }
  }, [address, publicKey, isZkLogin, user])
  
  // Sign in with Google
  const signInWithGoogle = async (redirectTo?: string) => {
    try {
      await supabaseAuth.signInWithGoogle(redirectTo)
    } catch (error) {
      console.error('Error signing in with Google:', error)
      throw error
    }
  }
  
  // Sign out
  const signOut = async () => {
    try {
      await supabaseAuth.signOut()
      setSession(null)
      setUser(null)
    } catch (error) {
      console.error('Error signing out:', error)
      throw error
    }
  }
  
  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        userAccount,
        isLoading,
        signInWithGoogle,
        signOut,
      }}
    >
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  const context = useContext(AuthContext)
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  
  return context
}
