import { Duel, DuelState } from '@/lib/duel/Duel'
import { PropsWithChildren, useEffect, useState } from 'react'
import { toast } from 'sonner'
import { OffChainDuelContext, OffChainDuelContextValue, PracticeDuelState } from './useOffChainDuel'

export function OffChainDuelProvider({
  children,
  currentWizardId = 'player',
  opponentId = 'opponent',
}: PropsWithChildren<{ currentWizardId?: string; opponentId?: string }>) {
  const [duelState, setDuelState] = useState<PracticeDuelState>('loading')
  const [duel] = useState<Duel>(() => new Duel('practice', currentWizardId, opponentId, false))
  const [duelData, setDuelData] = useState<DuelState>(duel.getState())

  // Update duel state based on duel conditions
  useEffect(() => {
    const updateState = () => {
      const newDuelData = duel.getState()
      setDuelData(newDuelData)

      const newDuelState: PracticeDuelState = duel.hasFinished()
        ? 'finished'
        : duel.hasStarted()
          ? 'started'
          : 'pending'

      setDuelState(newDuelState)
    }

    updateState()

    // Set up interval to check if duel has started
    const intervalId = setInterval(() => {
      if (duelState === 'pending' && duel.hasStarted()) {
        updateState()
      }
    }, 1000)

    return () => clearInterval(intervalId)
  }, [duel, duelState])

  // Start the duel with a countdown
  const startDuel: OffChainDuelContextValue['startDuel'] = (args, opts = {}) => {
    try {
      duel.startDuel(args.countdownSeconds)
      setDuelData(duel.getState())
      opts.onSuccess?.()
    } catch (error) {
      toast.error('Failed to start duel')
      opts.onError?.(error)
    } finally {
      opts.onSettled?.()
    }
  }

  // Cast a spell in the duel
  const castSpell: OffChainDuelContextValue['castSpell'] = (spellName, targetType, opts = {}) => {
    try {
      const targetId = targetType === 'self' ? currentWizardId : opponentId
      const success = duel.castSpell(currentWizardId, spellName, targetId)

      if (success) {
        setDuelData(duel.getState())
        toast.success(`Cast ${spellName} on ${targetType}!`)
        opts.onSuccess?.()
      } else {
        toast.warning('Failed to cast spell')
        opts.onError?.(new Error('Failed to cast spell'))
      }
    } catch (error) {
      toast.error('Error casting spell')
      opts.onError?.(error)
    } finally {
      opts.onSettled?.()
    }
  }

  // Determine winner and loser
  const winner = duel.getWinner()
  const loser = duel.getLoser()

  // AI opponent response in practice mode
  useEffect(() => {
    if (duelState === 'started' && !duel.hasFinished()) {
      const aiResponseTimeout = setTimeout(() => {
        // Simple AI: randomly choose a spell to cast
        const spells = ['arrow', 'choke', 'deflect', 'throw']
        const randomSpell = spells[Math.floor(Math.random() * spells.length)]

        const success = duel.castSpell(opponentId, randomSpell, currentWizardId)
        if (success) {
          setDuelData(duel.getState())
          toast.info(`Opponent cast ${randomSpell}!`)
        }
      }, 3000)

      return () => clearTimeout(aiResponseTimeout)
    }
  }, [duelState, duelData, duel, currentWizardId, opponentId])

  return (
    <OffChainDuelContext.Provider
      value={{
        duel,
        duelState,
        duelData,
        startDuel,
        castSpell,
        winner,
        loser,
        isLoading: duelState === 'loading',
        currentWizardId,
        opponentId,
      }}
    >
      {children}
    </OffChainDuelContext.Provider>
  )
}
