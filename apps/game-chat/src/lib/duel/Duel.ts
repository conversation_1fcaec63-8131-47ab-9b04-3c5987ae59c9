/**
 * Duel class to manage both practice and on-chain duels
 * Handles wizard force and effects for both wizards
 */

export type WizardEffects = [number, number, number]; // [choke, throw, deflect]
export type SpellType = 'damage' | 'effect';

export interface SpellSpec {
  name: string;
  cost: number;
  type: SpellType;
  damage?: number;
  effect?: {
    type: 'choke' | 'throw' | 'deflect';
    value: number;
  };
}

// Predefined spells with their costs and effects
export const SPELLS: Record<string, SpellSpec> = {
  arrow: {
    name: 'arrow',
    cost: 4,
    type: 'damage',
    damage: 15,
  },
  choke: {
    name: 'choke',
    cost: 5,
    type: 'effect',
    effect: {
      type: 'choke',
      value: 1,
    },
  },
  deflect: {
    name: 'deflect',
    cost: 3,
    type: 'effect',
    effect: {
      type: 'deflect',
      value: 1,
    },
  },
  throw: {
    name: 'throw',
    cost: 2,
    type: 'effect',
    effect: {
      type: 'throw',
      value: 1,
    },
  },
  summon: {
    name: 'summon',
    cost: 6,
    type: 'damage',
    damage: 21,
  },
};

export interface DuelWizard {
  id: string;
  force: number;
  effects: WizardEffects;
}

export interface DuelState {
  id: string;
  wizard1: DuelWizard;
  wizard2: DuelWizard;
  startedAt: number;
  isOnChain: boolean;
}

export class Duel {
  private state: DuelState;

  /**
   * Create a new Duel instance
   * @param id - Unique identifier for the duel
   * @param wizard1Id - ID of the first wizard
   * @param wizard2Id - ID of the second wizard
   * @param isOnChain - Whether this duel is managed on-chain
   * @param initialForce - Initial force for both wizards (default: 128)
   */
  constructor(
    id: string,
    wizard1Id: string,
    wizard2Id: string,
    isOnChain: boolean = false,
    initialForce: number = 128
  ) {
    this.state = {
      id,
      wizard1: {
        id: wizard1Id,
        force: initialForce,
        effects: [0, 0, 0],
      },
      wizard2: {
        id: wizard2Id,
        force: initialForce,
        effects: [0, 0, 0],
      },
      startedAt: 0,
      isOnChain,
    };
  }

  /**
   * Get the current duel state
   */
  getState(): DuelState {
    return { ...this.state };
  }

  /**
   * Start the duel with a countdown
   * @param countdownSeconds - Seconds to countdown before duel starts
   */
  startDuel(countdownSeconds: number = 15): void {
    if (this.state.startedAt !== 0) {
      throw new Error('Duel already started');
    }
    this.state.startedAt = Date.now() + countdownSeconds * 1000;
  }

  /**
   * Check if the duel has started
   */
  hasStarted(): boolean {
    return this.state.startedAt !== 0 && Date.now() >= this.state.startedAt;
  }

  /**
   * Check if the duel has finished (one wizard has 0 force)
   */
  hasFinished(): boolean {
    return this.state.wizard1.force === 0 || this.state.wizard2.force === 0;
  }

  /**
   * Get the winner of the duel
   * @returns The ID of the winning wizard, or null if no winner yet
   */
  getWinner(): string | null {
    if (this.state.wizard1.force === 0) {
      return this.state.wizard2.id;
    }
    if (this.state.wizard2.force === 0) {
      return this.state.wizard1.id;
    }
    return null;
  }

  /**
   * Get the loser of the duel
   * @returns The ID of the losing wizard, or null if no loser yet
   */
  getLoser(): string | null {
    if (this.state.wizard1.force === 0) {
      return this.state.wizard1.id;
    }
    if (this.state.wizard2.force === 0) {
      return this.state.wizard2.id;
    }
    return null;
  }

  /**
   * Cast a spell in the duel
   * @param casterId - ID of the wizard casting the spell
   * @param spellName - Name of the spell to cast
   * @param targetId - ID of the target wizard
   * @returns Success status of the spell cast
   */
  castSpell(casterId: string, spellName: string, targetId: string): boolean {
    // Check if duel has started
    if (!this.hasStarted() || this.hasFinished()) {
      return false;
    }

    // Get spell specification
    const spell = SPELLS[spellName.toLowerCase()];
    if (!spell) {
      return false;
    }

    // Get caster and target
    const caster = this.getWizardById(casterId);
    const target = this.getWizardById(targetId);
    
    if (!caster || !target) {
      return false;
    }

    // Check if caster has enough force
    if (caster.force < spell.cost) {
      return false;
    }

    // Reduce caster's force by spell cost
    caster.force -= spell.cost;

    // Apply spell effects
    if (spell.type === 'damage') {
      this.applyDamage(caster, target, spell.damage || 0);
    } else if (spell.type === 'effect' && spell.effect) {
      this.applyEffect(caster, target, spell.effect.type, spell.effect.value);
    }

    return true;
  }

  /**
   * Get a wizard by ID
   * @param wizardId - ID of the wizard to get
   * @returns The wizard object or null if not found
   */
  private getWizardById(wizardId: string): DuelWizard | null {
    if (this.state.wizard1.id === wizardId) {
      return this.state.wizard1;
    }
    if (this.state.wizard2.id === wizardId) {
      return this.state.wizard2;
    }
    return null;
  }

  /**
   * Apply damage from a spell
   * @param caster - Wizard casting the spell
   * @param target - Target wizard
   * @param damage - Amount of damage to apply
   */
  private applyDamage(caster: DuelWizard, target: DuelWizard, damage: number): void {
    // Apply game rules similar to the on-chain engine.settle function
    
    // If target has deflect, nullify damage and consume deflect
    if (target.effects[2] > 0) {
      target.effects[2] = 0;
      return;
    }

    // Apply damage to target's force
    target.force = Math.max(0, target.force - damage);
    
    // Reset caster's throw and choke effects when dealing damage
    caster.effects[0] = 0; // Reset choke
    caster.effects[1] = 0; // Reset throw
    
    // If target has choke level 3 or higher, reduce force to 0
    if (target.effects[0] >= 3) {
      target.force = 0;
    }
  }

  /**
   * Apply an effect from a spell
   * @param caster - Wizard casting the spell
   * @param target - Target wizard
   * @param effectType - Type of effect to apply
   * @param value - Value of the effect
   */
  private applyEffect(
    caster: DuelWizard,
    target: DuelWizard,
    effectType: 'choke' | 'throw' | 'deflect',
    value: number
  ): void {
    // Apply game rules similar to the on-chain engine.settle function
    
    switch (effectType) {
      case 'choke':
        // Choke is applied to the target and is compoundable up to 3
        target.effects[0] = Math.min(3, target.effects[0] + value);
        
        // If choke is applied, remove deflect
        if (target.effects[0] > 0) {
          target.effects[2] = 0; // Remove deflect
        }
        
        // If choke reaches level 3, reduce force to 0
        if (target.effects[0] >= 3) {
          target.force = 0;
        }
        break;
        
      case 'throw':
        // Throw is applied to the target and is not compoundable
        target.effects[1] = 1;
        
        // If throw is applied and target doesn't have deflect, remove choke from caster
        if (target.effects[2] === 0) {
          caster.effects[0] = 0; // Remove choke from caster
        }
        break;
        
      case 'deflect':
        // Deflect is applied to the caster (self) and is not compoundable
        caster.effects[2] = 1;
        break;
    }
  }
}
