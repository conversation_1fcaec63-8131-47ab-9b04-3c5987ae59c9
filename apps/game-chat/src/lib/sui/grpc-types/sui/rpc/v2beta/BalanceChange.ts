// Original file: src/lib/sui/grpc/balance_change.proto


export interface BalanceChange {
  'address'?: (string);
  'coinType'?: (string);
  'amount'?: (string);
  '_address'?: "address";
  '_coinType'?: "coinType";
  '_amount'?: "amount";
}

export interface BalanceChange__Output {
  'address'?: (string);
  'coinType'?: (string);
  'amount'?: (string);
  '_address'?: "address";
  '_coinType'?: "coinType";
  '_amount'?: "amount";
}
