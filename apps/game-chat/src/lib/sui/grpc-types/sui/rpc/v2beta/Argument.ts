// Original file: src/lib/sui/grpc/argument.proto


// Original file: src/lib/sui/grpc/argument.proto

export const _sui_rpc_v2beta_Argument_ArgumentKind = {
  ARGUMENT_KIND_UNKNOWN: 'ARGUMENT_KIND_UNKNOWN',
  GAS: 'GAS',
  INPUT: 'INPUT',
  RESULT: 'RESULT',
} as const;

export type _sui_rpc_v2beta_Argument_ArgumentKind =
  | 'ARGUMENT_KIND_UNKNOWN'
  | 0
  | 'GAS'
  | 1
  | 'INPUT'
  | 2
  | 'RESULT'
  | 3

export type _sui_rpc_v2beta_Argument_ArgumentKind__Output = typeof _sui_rpc_v2beta_Argument_ArgumentKind[keyof typeof _sui_rpc_v2beta_Argument_ArgumentKind]

export interface Argument {
  'kind'?: (_sui_rpc_v2beta_Argument_ArgumentKind);
  'index'?: (number);
  'subresult'?: (number);
  '_kind'?: "kind";
  '_index'?: "index";
  '_subresult'?: "subresult";
}

export interface Argument__Output {
  'kind'?: (_sui_rpc_v2beta_Argument_ArgumentKind__Output);
  'index'?: (number);
  'subresult'?: (number);
  '_kind'?: "kind";
  '_index'?: "index";
  '_subresult'?: "subresult";
}
