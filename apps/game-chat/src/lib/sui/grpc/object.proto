// Copyright (c) Mysten Labs, Inc.
// SPDX-License-Identifier: Apache-2.0

syntax = "proto3";

package sui.rpc.v2beta;

import "sui/rpc/v2beta/bcs.proto";
import "sui/rpc/v2beta/owner.proto";

// An object on the Sui blockchain.
message Object {
  // This Object serialized as BCS.
  optional Bcs bcs = 1;

  // `ObjectId` for this object.
  optional string object_id = 2;

  // Version of the object.
  optional uint64 version = 3;

  // The digest of this Object.
  optional string digest = 4;

  // Owner of the object.
  optional Owner owner = 5;

  // The type of this object.
  //
  // This will be 'package' for packages and a StructTag for move structs.
  optional string object_type = 6;

  // DEPRECATED this field is no longer used to determine whether a tx can transfer this
  // object. Instead, it is always calculated from the objects type when loaded in execution.
  //
  // Only set for Move structs
  optional bool has_public_transfer = 7;

  // BCS bytes of a Move struct value.
  //
  // Only set for Move structs
  optional Bcs contents = 8;

  // Set of modules defined by this package.
  //
  // Only set for Packages
  repeated MoveModule modules = 9;

  // Maps struct/module to a package version where it was first defined, stored as a vector for
  // simple serialization and deserialization.
  //
  // Only set for Packages
  repeated TypeOrigin type_origin_table = 10;

  // For each dependency, maps original package ID to the info about the (upgraded) dependency
  // version that this package is using.
  //
  // Only set for Packages
  repeated UpgradeInfo linkage_table = 11;

  // The digest of the transaction that created or last mutated this object
  optional string previous_transaction = 12;

  // The amount of SUI to rebate if this object gets deleted.
  // This number is re-calculated each time the object is mutated based on
  // the present storage gas price.
  optional uint64 storage_rebate = 13;
}

// Module defined by a package.
message MoveModule {
  // Name of the module.
  optional string name = 1;
  // Serialized bytecode of the module.
  optional bytes contents = 2;
}

// Identifies a struct and the module it was defined in.
message TypeOrigin {
  optional string module_name = 1;
  optional string struct_name = 2;
  optional string package_id = 3;
}

/// Upgraded package info for the linkage table.
message UpgradeInfo {
  // ID of the original package.
  optional string original_id = 1;
  // ID of the upgraded package.
  optional string upgraded_id = 2;
  // Version of the upgraded package.
  optional uint64 upgraded_version = 3;
}
