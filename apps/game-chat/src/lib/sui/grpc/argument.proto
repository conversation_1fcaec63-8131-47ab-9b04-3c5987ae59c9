// Copyright (c) Mysten Labs, Inc.
// SPDX-License-Identifier: Apache-2.0

syntax = "proto3";

package sui.rpc.v2beta;

// An argument to a programmable transaction command.
message Argument {
  enum ArgumentKind {
    ARGUMENT_KIND_UNKNOWN = 0;

    // The gas coin.
    GAS = 1;

    // One of the input objects or primitive values (from
    // `ProgrammableTransaction` inputs).
    INPUT = 2;

    // The result of another command (from `ProgrammableTransaction` commands).
    RESULT = 3;
  }

  optional ArgumentKind kind = 1;

  // Index of an input or the result of another command based on `kind`.
  optional uint32 index = 2;

  // Used to access a nested result when `kind` is `RESULT`.
  optional uint32 subresult = 3;
}
