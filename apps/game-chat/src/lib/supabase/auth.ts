import { supabase } from './client'
import { User, Session, Provider } from '@supabase/supabase-js'

/**
 * Sign in with a third-party OAuth provider
 * @param provider The OAuth provider to use
 * @param redirectTo The URL to redirect to after successful sign in
 */
export const signInWithOAuth = async (provider: Provider, redirectTo?: string) => {
  const { data, error } = await supabase.auth.signInWithOAuth({
    provider,
    options: {
      redirectTo,
    },
  })
  
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Sign in with Google OAuth
 * @param redirectTo The URL to redirect to after successful sign in
 */
export const signInWithGoogle = async (redirectTo?: string) => {
  return signInWithOAuth('google', redirectTo)
}

/**
 * Sign out the current user
 */
export const signOut = async () => {
  const { error } = await supabase.auth.signOut()
  
  if (error) {
    throw error
  }
}

/**
 * Get the current user session
 * @returns The current session or null if not authenticated
 */
export const getSession = async (): Promise<Session | null> => {
  const { data, error } = await supabase.auth.getSession()
  
  if (error) {
    throw error
  }
  
  return data.session
}

/**
 * Get the current user
 * @returns The current user or null if not authenticated
 */
export const getCurrentUser = async (): Promise<User | null> => {
  const { data, error } = await supabase.auth.getUser()
  
  if (error) {
    throw error
  }
  
  return data.user
}

/**
 * Set up an auth state change listener
 * @param callback Function to call when auth state changes
 * @returns Unsubscribe function
 */
export const onAuthStateChange = (
  callback: (event: 'SIGNED_IN' | 'SIGNED_OUT' | 'USER_UPDATED' | 'TOKEN_REFRESHED', session: Session | null) => void
) => {
  const { data } = supabase.auth.onAuthStateChange((event, session) => {
    callback(event, session)
  })
  
  return data.subscription.unsubscribe
}

/**
 * Link a third-party OAuth account to the current user
 * @param provider The OAuth provider to link
 * @param redirectTo The URL to redirect to after successful linking
 */
export const linkWithOAuth = async (provider: Provider, redirectTo?: string) => {
  const { data, error } = await supabase.auth.linkIdentity({
    provider,
    options: {
      redirectTo,
    },
  })
  
  if (error) {
    throw error
  }
  
  return data
}

/**
 * Get user metadata from Supabase
 * @param userId The user ID to get metadata for
 */
export const getUserMetadata = async (userId: string) => {
  const { data, error } = await supabase
    .from('user_metadata')
    .select('*')
    .eq('user_id', userId)
    .single()
  
  if (error) {
    // If the error is 'not found', return null instead of throwing
    if (error.code === 'PGRST116') {
      return null
    }
    throw error
  }
  
  return data
}

/**
 * Update user metadata in Supabase
 * @param userId The user ID to update metadata for
 * @param metadata The metadata to update
 */
export const updateUserMetadata = async (userId: string, metadata: Record<string, any>) => {
  // Check if user metadata exists
  const existingMetadata = await getUserMetadata(userId)
  
  if (existingMetadata) {
    // Update existing metadata
    const { data, error } = await supabase
      .from('user_metadata')
      .update(metadata)
      .eq('user_id', userId)
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    return data
  } else {
    // Insert new metadata
    const { data, error } = await supabase
      .from('user_metadata')
      .insert({
        user_id: userId,
        ...metadata,
      })
      .select()
      .single()
    
    if (error) {
      throw error
    }
    
    return data
  }
}
