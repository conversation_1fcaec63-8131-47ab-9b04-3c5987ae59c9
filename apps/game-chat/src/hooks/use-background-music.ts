import { useEffect, useRef, useState } from 'react';

interface UseBackgroundMusicOptions {
  /**
   * Path to the audio file
   */
  src: string;
  /**
   * Whether the audio should loop
   */
  loop?: boolean;
  /**
   * Volume of the audio (0-1)
   */
  volume?: number;
  /**
   * Whether the audio should play automatically
   */
  autoPlay?: boolean;
  /**
   * Whether the audio should be preloaded
   */
  preload?: boolean;
}

/**
 * Hook for managing background music
 */
export function useBackgroundMusic({
  src,
  loop = true,
  volume = 0.5,
  autoPlay = true,
  preload = true,
}: UseBackgroundMusicOptions) {
  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [isLoaded, setIsLoaded] = useState(false);

  // Initialize audio element
  useEffect(() => {
    // Create audio element if it doesn't exist
    if (!audioRef.current) {
      const audio = new Audio();
      audio.loop = loop;
      audio.volume = volume;
      audio.preload = preload ? 'auto' : 'none';
      
      // Set up event listeners
      audio.addEventListener('canplaythrough', () => {
        setIsLoaded(true);
        if (autoPlay) {
          audio.play().catch(err => {
            console.warn('Failed to autoplay audio:', err);
          });
        }
      });
      
      audio.addEventListener('play', () => {
        setIsPlaying(true);
      });
      
      audio.addEventListener('pause', () => {
        setIsPlaying(false);
      });
      
      audio.addEventListener('ended', () => {
        if (!loop) {
          setIsPlaying(false);
        }
      });
      
      audioRef.current = audio;
    }
    
    // Update audio source if it changes
    if (audioRef.current.src !== src) {
      audioRef.current.src = src;
      setIsLoaded(false);
    }
    
    // Clean up event listeners
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current.src = '';
      }
    };
  }, [src, loop, volume, autoPlay, preload]);

  // Play function
  const play = () => {
    if (audioRef.current && isLoaded) {
      audioRef.current.play().catch(err => {
        console.warn('Failed to play audio:', err);
      });
    }
  };

  // Pause function
  const pause = () => {
    if (audioRef.current) {
      audioRef.current.pause();
    }
  };

  // Set volume function
  const setVolume = (newVolume: number) => {
    if (audioRef.current) {
      audioRef.current.volume = Math.max(0, Math.min(1, newVolume));
    }
  };

  // Fade out function
  const fadeOut = (duration: number = 1000) => {
    if (audioRef.current && isPlaying) {
      const startVolume = audioRef.current.volume;
      const fadeSteps = 20;
      const fadeInterval = duration / fadeSteps;
      const volumeStep = startVolume / fadeSteps;
      
      let currentStep = 0;
      
      const fadeIntervalId = setInterval(() => {
        currentStep++;
        const newVolume = startVolume - (volumeStep * currentStep);
        
        if (audioRef.current) {
          if (currentStep >= fadeSteps || newVolume <= 0) {
            audioRef.current.volume = 0;
            audioRef.current.pause();
            clearInterval(fadeIntervalId);
          } else {
            audioRef.current.volume = newVolume;
          }
        } else {
          clearInterval(fadeIntervalId);
        }
      }, fadeInterval);
    }
  };

  // Fade in function
  const fadeIn = (duration: number = 1000, targetVolume: number = volume) => {
    if (audioRef.current) {
      audioRef.current.volume = 0;
      
      audioRef.current.play().catch(err => {
        console.warn('Failed to play audio during fade in:', err);
        return;
      });
      
      const fadeSteps = 20;
      const fadeInterval = duration / fadeSteps;
      const volumeStep = targetVolume / fadeSteps;
      
      let currentStep = 0;
      
      const fadeIntervalId = setInterval(() => {
        currentStep++;
        const newVolume = volumeStep * currentStep;
        
        if (audioRef.current) {
          if (currentStep >= fadeSteps || newVolume >= targetVolume) {
            audioRef.current.volume = targetVolume;
            clearInterval(fadeIntervalId);
          } else {
            audioRef.current.volume = newVolume;
          }
        } else {
          clearInterval(fadeIntervalId);
        }
      }, fadeInterval);
    }
  };

  return {
    isPlaying,
    isLoaded,
    play,
    pause,
    setVolume,
    fadeOut,
    fadeIn,
  };
}
