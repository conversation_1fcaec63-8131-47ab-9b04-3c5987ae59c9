import { ButtonWithFx } from '@/components/ui/button';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { ParallaxProvider, Parallax } from 'react-scroll-parallax';

export function Landing() {
  const navigate = useNavigate()
  const handleLaunchGame = () => navigate('/practice')
  const handlePlayPvP = () => navigate('/d')

  return (
    <ParallaxProvider>
      <div className="min-h-screen relative">
        {/* Parallax Background */}
        <Parallax
          speed={-20}
          className="absolute inset-0 w-full h-full"
          style={{ zIndex: -1 }}
        >
          <div
            className="w-full h-[120vh] bg-gradient-to-b from-gray-50 to-gray-200 bg-cover bg-center bg-no-repeat"
            style={{
              backgroundImage: `url('/path/to/your/background-image.jpg')`, // Update with actual image path
            }}
          />
        </Parallax>

        {/* Content Overlay */}
        <div className="relative z-10 bg-black bg-opacity-20">
        {/* Hero Section */}
        <div className="container mx-auto px-4 py-16">
          <div className="flex flex-col items-center text-center">
            <h1 className="text-6xl font-bold mb-6 bg-gradient-to-r from-purple-600 to-blue-600 bg-clip-text text-transparent drop-shadow-lg">
              Wizards and Duels
            </h1>
            <p className="text-xl mb-8 max-w-3xl leading-relaxed drop-shadow-md">
              Enter the mystical realm of blockchain-powered wizard duels! Master powerful spells,
              engage in strategic real-time combat, and stake SUI tokens for epic rewards.
              Experience the future of gaming on the Sui blockchain.
            </p>

            <div className="flex flex-col sm:flex-row gap-4 mt-6">
              <ButtonWithFx onClick={handleLaunchGame} className="text-lg px-8 py-3">
                Start Practice
              </ButtonWithFx>
              <Button onClick={handlePlayPvP} variant="outline" className="text-lg px-8 py-3 bg-white bg-opacity-90 hover:bg-opacity-100">
                Play PvP
              </Button>
            </div>

            <div className="mt-6 flex items-center gap-4">
              <a
                href="https://x.com/wizardsandduels"
                target="_blank"
                rel="noopener noreferrer"
                className="hover:text-blue-200 transition-colors drop-shadow-md"
              >
                Follow @wizardsandduels
              </a>
            </div>
          </div>
        </div>

        {/* About the Game */}
        <div className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold mb-8 text-center">About Wizards and Duels</h2>
            <div className="max-w-4xl mx-auto text-lg text-gray-700 leading-relaxed">
              <p className="mb-6">
                <strong>Wizards and Duels</strong> is a revolutionary blockchain-based strategy game where players embody powerful wizards
                engaging in real-time magical combat. Built on the lightning-fast Sui blockchain, the game combines traditional
                turn-based strategy with modern real-time mechanics and cryptocurrency rewards.
              </p>
              <p className="mb-6">
                Each wizard starts with 128 force points and must strategically cast spells to defeat their opponent.
                The game features a sophisticated spell system with four core spells: <strong>Arrow</strong> (direct damage),
                <strong>Choke</strong> (damage over time), <strong>Throw</strong> (removes opponent's choke effects), and
                <strong>Deflect</strong> (nullifies incoming damage). Victory goes to the wizard who reduces their opponent's force to zero first.
              </p>
              <p>
                What sets Wizards and Duels apart is its integration with the Sui blockchain, enabling true ownership of game assets,
                transparent gameplay mechanics, and real cryptocurrency rewards for skilled players.
              </p>
            </div>
          </div>
        </div>

        {/* Game Modes */}
        <div className="bg-gradient-to-r from-purple-100 to-blue-100 bg-opacity-95 py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold mb-12 text-center">Game Modes</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <GameModeCard
                title="Practice Mode"
                description="Perfect your skills against AI opponents in a risk-free environment. Learn spell combinations, master timing, and develop winning strategies before entering PvP battles."
                features={["AI opponents", "No stakes required", "Tutorial guidance", "Skill development"]}
                buttonText="Start Practice"
                onClick={handleLaunchGame}
              />
              <GameModeCard
                title="PvP Duels"
                description="Challenge real players in high-stakes magical combat. Stake SUI tokens (0-100 SUI) and compete for the entire prize pool. Winner takes all!"
                features={["Real-time PvP", "SUI token stakes", "Prize pools", "Competitive ranking"]}
                buttonText="Enter Duelground"
                onClick={handlePlayPvP}
              />
            </div>
          </div>
        </div>

        {/* Game Features */}
        <div className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold mb-12 text-center">Core Features</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              <FeatureCard
                title="Strategic Spell Combat"
                description="Master four unique spells with distinct effects. Combine Arrow damage, Choke effects, Throw counters, and Deflect shields for tactical advantage."
              />
              <FeatureCard
                title="SUI Token Stakes"
                description="Stake 0-100 SUI tokens in PvP duels. Winners claim the entire prize pool, making every duel a high-stakes magical battle."
              />
              <FeatureCard
                title="Real-time Combat"
                description="Experience fast-paced, real-time spell casting with immediate feedback. No waiting for turns - cast spells as fast as you can type!"
              />
              <FeatureCard
                title="Immersive Audio"
                description="Enjoy atmospheric background music and satisfying spell sound effects that bring the magical world to life."
              />
              <FeatureCard
                title="Competitive Matchmaking"
                description="Fair matchmaking system pairs players with similar stake amounts, ensuring balanced and exciting duels for everyone."
              />
              <FeatureCard
                title="Blockchain Powered"
                description="Built on Sui blockchain for transparent gameplay, true asset ownership, and instant cryptocurrency rewards."
              />
            </div>
          </div>
        </div>

        {/* How to Play */}
        <div className="bg-gray-50 bg-opacity-95 py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold mb-12 text-center">How to Play</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <StepCard
                number="1"
                title="Connect & Choose"
                description="Connect your Sui wallet and choose between Practice mode (free) or PvP duels (stake SUI tokens)."
              />
              <StepCard
                number="2"
                title="Enter Battle"
                description="Join the waitroom for PvP matchmaking or start practice against AI opponents immediately."
              />
              <StepCard
                number="3"
                title="Cast Spells"
                description="Use Arrow (@arrow), Choke (@choke), Throw (@throw), and Deflect (!deflect) spells strategically in real-time."
              />
              <StepCard
                number="4"
                title="Claim Victory"
                description="Reduce your opponent's force to zero to win the duel and claim your SUI prize pool!"
              />
            </div>

            <div className="mt-12 text-center">
              <div className="bg-white p-6 rounded-lg shadow-md max-w-2xl mx-auto">
                <h3 className="text-xl font-bold mb-4">Spell Quick Reference</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div><strong>@arrow</strong> - Direct damage (15 force)</div>
                  <div><strong>@choke</strong> - Damage over time (stackable)</div>
                  <div><strong>@throw</strong> - Removes opponent's choke</div>
                  <div><strong>!deflect</strong> - Blocks next incoming damage</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* What's New */}
        <div className="py-16">
          <div className="container mx-auto px-4">
            <h2 className="text-4xl font-bold mb-12 text-center">What's New & Coming Soon</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
              <div className="bg-green-50 border-l-4 border-green-500 p-6 rounded-lg">
                <h3 className="text-2xl font-bold mb-4 text-green-800">Recently Completed</h3>
                <ul className="space-y-2 text-green-700">
                  <li>• <strong>SUI Token Staking:</strong> Players can now stake 0-100 SUI in PvP duels</li>
                  <li>• <strong>Prize Pool System:</strong> Winners claim the entire prize pool</li>
                  <li>• <strong>Enhanced Audio:</strong> Spell sound effects and atmospheric music</li>
                  <li>• <strong>Practice Mode:</strong> Demo duels against NPCs for learning</li>
                  <li>• <strong>Auto-signing:</strong> Streamlined transaction experience</li>
                  <li>• <strong>Spell Buttons:</strong> Quick-cast UI for faster gameplay</li>
                </ul>
              </div>

              <div className="bg-blue-50 border-l-4 border-blue-500 p-6 rounded-lg">
                <h3 className="text-2xl font-bold mb-4 text-blue-800">Coming Soon</h3>
                <ul className="space-y-2 text-blue-700">
                  <li>• <strong>Visual Spell Effects:</strong> Stunning animations for spell casting</li>
                  <li>• <strong>Welcome Rewards:</strong> New player SUI token bonuses</li>
                  <li>• <strong>WnD Reward Token:</strong> Exclusive game token with utility</li>
                  <li>• <strong>Wizard NFTs:</strong> Collectible wizards with unique abilities</li>
                  <li>• <strong>Spell Schools:</strong> Multiple magic schools with specialized spells</li>
                  <li>• <strong>Equipment System:</strong> Artifacts and magical items</li>
                  <li>• <strong>Leaderboards:</strong> Competitive rankings and tournaments</li>
                </ul>
              </div>
            </div>

            <div className="mt-12 text-center">
              <p className="text-lg text-gray-600 mb-4">
                Follow our development progress and get the latest updates:
              </p>
              <a
                href="https://x.com/wizardsandduels"
                target="_blank"
                rel="noopener noreferrer"
                className="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
              >
                Follow @wizardsandduels on X
              </a>
            </div>
          </div>
        </div>

        {/* Final Call to Action */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 bg-opacity-95 py-16">
          <div className="container mx-auto px-4 text-center">
            <h2 className="text-4xl font-bold mb-6 text-white">Ready to Begin Your Magical Journey?</h2>
            <p className="text-xl mb-8 text-purple-100 max-w-2xl mx-auto">
              Join thousands of wizards already battling for glory and SUI rewards.
              Start with practice mode or dive straight into competitive PvP duels!
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-8">
              <ButtonWithFx onClick={handleLaunchGame} className="bg-white text-purple-600 hover:bg-gray-100 text-lg px-8 py-3">
                Start Practice Mode
              </ButtonWithFx>
              <Button onClick={handlePlayPvP} variant="outline" className="border-white text-white hover:bg-white hover:text-purple-600 text-lg px-8 py-3">
                Enter PvP Arena
              </Button>
            </div>

            <div className="text-purple-100">
              <p className="mb-2">Follow us for updates and community events:</p>
              <a
                href="https://x.com/wizardsandduels"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-purple-200 transition-colors font-semibold"
              >
                @wizardsandduels
              </a>
            </div>
          </div>
        </div>

        {/* Footer */}
        <footer className="bg-gray-900 bg-opacity-95 py-12">
          <div className="container mx-auto px-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 text-center md:text-left">
              <div>
                <h3 className="text-xl font-bold mb-4 text-white">Wizards and Duels</h3>
                <p className="text-gray-400">
                  The premier blockchain-based wizard dueling game on Sui.
                  Experience real-time magical combat with cryptocurrency rewards.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-bold mb-4 text-white">Quick Links</h3>
                <div className="space-y-2">
                  <button onClick={handleLaunchGame} className="block text-gray-400 hover:text-white transition-colors">
                    Practice Mode
                  </button>
                  <button onClick={handlePlayPvP} className="block text-gray-400 hover:text-white transition-colors">
                    PvP Duels
                  </button>
                  <a href="https://x.com/wizardsandduels" target="_blank" rel="noopener noreferrer" className="block text-gray-400 hover:text-white transition-colors">
                    Twitter/X
                  </a>
                </div>
              </div>

              <div>
                <h3 className="text-xl font-bold mb-4 text-white">Built On</h3>
                <p className="text-gray-400 mb-2">Sui Blockchain</p>
                <p className="text-gray-400 text-sm">
                  Powered by Move smart contracts for transparent,
                  secure, and lightning-fast gameplay.
                </p>
              </div>
            </div>

            <div className="border-t border-gray-800 mt-8 pt-8 text-center">
              <p className="text-gray-400">
                © 2024 Wizards and Duels. Built for Sui Overflow 2025 Hackathon.
              </p>
            </div>
          </div>
        </footer>
      </div>
    </ParallaxProvider>
  )
}

function FeatureCard({ title, description }: { title: string; description: string }) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md hover:shadow-lg transition-shadow">
      <h3 className="text-xl font-bold mb-3">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  )
}

function GameModeCard({
  title,
  description,
  features,
  buttonText,
  onClick
}: {
  title: string
  description: string
  features: string[]
  buttonText: string
  onClick: () => void
}) {
  return (
    <div className="bg-white p-8 rounded-lg shadow-lg hover:shadow-xl transition-shadow">
      <h3 className="text-2xl font-bold mb-4 text-center">{title}</h3>
      <p className="text-gray-600 mb-6 text-center">{description}</p>

      <ul className="space-y-2 mb-6">
        {features.map((feature, index) => (
          <li key={index} className="flex items-center text-sm text-gray-700">
            <span className="text-green-500 mr-2">✓</span>
            {feature}
          </li>
        ))}
      </ul>

      <div className="text-center">
        <ButtonWithFx onClick={onClick} className="w-full">
          {buttonText}
        </ButtonWithFx>
      </div>
    </div>
  )
}

function StepCard({
  number,
  title,
  description,
}: {
  number: string
  title: string
  description: string
}) {
  return (
    <div className="bg-white p-6 rounded-lg shadow-md relative hover:shadow-lg transition-shadow">
      <div className="absolute -top-4 -left-4 w-10 h-10 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center font-bold text-white">
        {number}
      </div>
      <h3 className="text-xl font-bold mb-3 mt-2">{title}</h3>
      <p className="text-gray-600">{description}</p>
    </div>
  )
}
