import { RealtimeChat } from '@/components/realtime-chat'
import { useCallback } from 'react'
import { Link } from '@/components/Link'
import { OffChainDuelProvider, useOffChainDuel } from '@/context/OffChainDuelContext'
import { Loader } from '@/components/Loader'
import { WizardEffects } from '@/duel/WizardEffects'

export function PracticeDuel() {
  return (
    <div className="w-[460px] h-full mx-auto px-4">
      <OffChainDuelProvider currentWizardId="player" opponentId="opponent">
        <PracticeDuelContent />
      </OffChainDuelProvider>
    </div>
  )
}

function PracticeDuelContent() {
  const { duelState, isLoading } = useOffChainDuel()

  // Render different screens based on duel state
  if (isLoading) {
    return <Loader />
  }

  return (
    <div className="flex flex-col h-full">
      {duelState === 'pending' && <Start />}
      {duelState === 'started' && <Action />}
      {duelState === 'finished' && <Result />}
    </div>
  )
}

function Start() {
  const { startDuel, duelData } = useOffChainDuel()

  const handleStartDuel = () => {
    startDuel({ countdownSeconds: 5 })
  }

  return (
    <div className="flex flex-col items-center justify-center p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">Practice Duel</h2>

      <div className="flex justify-between w-full mb-6">
        <div className="text-center">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <span className="text-xl">🧙</span>
          </div>
          <p className="font-semibold">Wooden Target</p>
          <p className="text-sm text-gray-600">Force: {duelData.wizard2.force}</p>
        </div>

        <div className="text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <span className="text-xl">🧙‍♂️</span>
          </div>
          <p className="font-semibold">You</p>
          <p className="text-sm text-gray-600">Force: {duelData.wizard1.force}</p>
        </div>
      </div>

      <button
        onClick={handleStartDuel}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors w-full"
      >
        Start Practice Duel
      </button>

      <div className="mt-4 text-center">
        <p className="text-sm text-gray-600 mb-2">
          Practice mode: Cast spells with @ (opponent) or ! (self)
        </p>
        <Link to="/" className="text-blue-500 hover:underline">
          Back to Home
        </Link>
      </div>
    </div>
  )
}

function Action() {
  const { duelData, castSpell, currentWizardId, opponentId } = useOffChainDuel()

  const handleCastSpell = useCallback(
    (userInput: string) => {
      const message = userInput.trim()
      if (!message) return

      const targetChar = message[0]
      const isSpell = targetChar === '@' || targetChar === '!'

      if (!isSpell) return // Regular chat message

      const spellName = message.slice(1).trim().toLowerCase()
      const targetType = targetChar === '@' ? 'opponent' : 'self'

      castSpell(spellName, targetType)
    },
    [castSpell]
  )

  // Get wizard and opponent data
  const wizard = duelData.wizard1.id === currentWizardId ? duelData.wizard1 : duelData.wizard2
  const opponent = duelData.wizard1.id === opponentId ? duelData.wizard1 : duelData.wizard2

  const wizardForce = wizard.force
  const opponentForce = opponent.force
  const wizardEffects = wizard.effects
  const opponentEffects = opponent.effects

  return (
    <>
      <RealtimeChat
        roomName="practice-duel"
        username="Practice Wizard"
        onMessage={handleCastSpell}
      />
      <div className="flex flex-col w-full">
        <div className="w-full bg-gray-100 h-4 rounded-full overflow-hidden mb-2">
          <div className="bg-blue-500 h-full" style={{ width: `${(wizardForce / 128) * 100}%` }} />
        </div>
        <div className="w-full bg-gray-100 h-4 rounded-full overflow-hidden">
          <div className="bg-red-500 h-full" style={{ width: `${(opponentForce / 128) * 100}%` }} />
        </div>

        <div className="flex justify-between items-start py-8 px-4 w-full">
          <div className="flex flex-col items-center w-1/3">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-2">
              <span className="text-xl">🧙</span>
            </div>
            <p className="font-semibold text-sm">Wooden Target</p>
            <div className="mt-2 min-h-[30px]">
              <WizardEffects effects={opponentEffects} />
            </div>
          </div>

          <div className="text-lg font-bold flex items-center">VS</div>

          <div className="flex flex-col items-center w-1/3">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <span className="text-xl">🧙‍♂️</span>
            </div>
            <p className="font-semibold text-sm">You</p>
            <div className="mt-2 min-h-[30px]">
              <WizardEffects effects={wizardEffects} />
            </div>
          </div>
        </div>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600 mb-2">
            Practice mode: Cast spells with @ (opponent) or ! (self)
          </p>
          <Link to="/" className="text-blue-500 hover:underline">
            Back to Home
          </Link>
        </div>
      </div>
    </>
  )
}

function Result() {
  const { winner, duelData } = useOffChainDuel()

  const isWinner = winner === 'player'

  return (
    <div className="flex flex-col items-center justify-center p-6 bg-white rounded-lg shadow-md">
      <h2 className="text-2xl font-bold mb-6 text-center">
        {isWinner ? 'Victory!' : 'Defeat!'}
      </h2>

      <div className="flex justify-between w-full mb-6">
        <div className="text-center">
          <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <span className="text-xl">🧙</span>
          </div>
          <p className="font-semibold">Wooden Target</p>
          <p className="text-sm text-gray-600">Force: {duelData.wizard2.force}</p>
        </div>

        <div className="text-center">
          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
            <span className="text-xl">🧙‍♂️</span>
          </div>
          <p className="font-semibold">You</p>
          <p className="text-sm text-gray-600">Force: {duelData.wizard1.force}</p>
        </div>
      </div>

      <p className="text-lg font-semibold mb-4">
        {isWinner
          ? 'You have defeated your opponent!'
          : 'You have been defeated!'}
      </p>

      <Link to="/" className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors">
        Back to Home
      </Link>
    </div>
  )
}
