import { RealtimeChat } from '@/components/realtime-chat'
import { useState, useCallback } from 'react'
import { Link } from '@/components/Link'
import { toast } from 'sonner'

export function PracticeDuel() {
  return (
    <div className="w-[460px] h-full mx-auto px-4">
      <Action />
    </div>
  )
}

function Action() {
  const [wizardEffects, setWizardEffects] = useState<string[]>([])
  const [opponentEffects, setOpponentEffects] = useState<string[]>([])
  const [wizardForce, setWizardForce] = useState(100)
  const [opponentForce, setOpponentForce] = useState(100)

  const handleCastSpell = useCallback(
    (userInput: string) => {
      const message = userInput.trim()
      if (!message) return

      const targetChar = message[0]
      const isSpell = targetChar === '@' || targetChar === '!'

      if (!isSpell) return // Regular chat message

      const spellName = message.slice(1).trim().toLowerCase()
      const spellCost = 10 // Simplified cost for practice mode

      // Simple spell simulation
      if (targetChar === '@') {
        // Attack opponent
        if (wizardForce >= spellCost) {
          setWizardForce((prev) => prev - spellCost)
          setOpponentForce((prev) => Math.max(0, prev - 15))
          setOpponentEffects((prev) => [...prev, spellName])
          toast.success(`Cast ${spellName} on opponent!`)
        } else {
          toast.warning('Not enough force to cast spell')
        }
      } else if (targetChar === '!') {
        // Self-buff
        if (wizardForce >= spellCost) {
          setWizardForce((prev) => prev - spellCost)
          setWizardEffects((prev) => [...prev, spellName])
          toast.success(`Cast ${spellName} on yourself!`)
        } else {
          toast.warning('Not enough force to cast spell')
        }
      } else {
        // not a spell, a text message
      }

      // Simple AI response
      setTimeout(() => {
        if (opponentForce >= spellCost) {
          setOpponentForce((prev) => prev - spellCost)
          setWizardForce((prev) => Math.max(0, prev - 10))
          setWizardEffects((prev) => [...prev, 'counterSpell'])
          toast.info('Opponent cast counterSpell!')
        }
      }, 2000)
    },
    [wizardForce, opponentForce]
  )

  return (
    <>
      <RealtimeChat
        roomName="practice-duel"
        username="Practice Wizard"
        onMessage={handleCastSpell}
      />
      <div className="flex flex-col w-full">
        <div className="w-full bg-gray-100 h-4 rounded-full overflow-hidden mb-2">
          <div className="bg-blue-500 h-full" style={{ width: `${wizardForce}%` }} />
        </div>
        <div className="w-full bg-gray-100 h-4 rounded-full overflow-hidden">
          <div className="bg-red-500 h-full" style={{ width: `${opponentForce}%` }} />
        </div>

        <div className="flex justify-between items-start py-8 px-4 w-full">
          <div className="flex flex-col items-center w-1/3">
            <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-2">
              <span className="text-xl">🧙</span>
            </div>
            <p className="font-semibold text-sm">Wooden Target</p>
            <div className="mt-2 min-h-[30px]">
              {opponentEffects.map((effect, i) => (
                <span
                  key={i}
                  className="inline-block bg-red-100 text-red-800 text-xs px-2 py-1 rounded mr-1 mb-1"
                >
                  {effect}
                </span>
              ))}
            </div>
          </div>

          <div className="text-lg font-bold flex items-center">VS</div>

          <div className="flex flex-col items-center w-1/3">
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
              <span className="text-xl">🧙‍♂️</span>
            </div>
            <p className="font-semibold text-sm">You</p>
            <div className="mt-2 min-h-[30px]">
              {wizardEffects.map((effect, i) => (
                <span
                  key={i}
                  className="inline-block bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded mr-1 mb-1"
                >
                  {effect}
                </span>
              ))}
            </div>
          </div>
        </div>

        <div className="mt-4 text-center">
          <p className="text-sm text-gray-600 mb-2">
            Practice mode: Cast spells with @ (opponent) or ! (self)
          </p>
          <Link to="/" className="text-blue-500 hover:underline">
            Back to Home
          </Link>
        </div>
      </div>
    </>
  )
}

// TODO: add OffChainDuelContext that will be used in practice mode it will be using Duel class that have constructor to manage both practice and on chain duels, and methods to update wiazard force and effects. PracticeDuel must use Duel calss to manage duel UI state 
