import { Button } from '@/components/ui/button'
import { cn } from '@/lib/utils'

interface SpellButtonsProps {
  onCastSpell: (spell: string) => void
  className?: string
}

/**
 * SpellButtons component displays buttons for casting spells
 * @param onCastSpell - Callback function when a spell button is clicked
 * @param className - Additional CSS classes
 */
export function SpellButtons({ onCastSpell, className }: SpellButtonsProps) {
  const spells = [
    { name: '@arrow', label: 'Arrow', description: 'Cast arrow spell on opponent' },
    { name: '@choke', label: 'Choke', description: 'Cast choke spell on opponent' },
    { name: '@throw', label: 'Throw', description: 'Cast throw spell on opponent' },
    { name: '!deflect', label: 'Deflect', description: 'Cast deflect spell on yourself' },
  ]

  return (
    <div className={cn('flex flex-col gap-2 p-2', className)}>
      {spells.map((spell) => (
        <Button
          key={spell.name}
          variant="outline"
          size="sm"
          className="justify-start text-left"
          title={spell.description}
          onClick={() => onCastSpell(spell.name)}
        >
          {spell.label}
        </Button>
      ))}
    </div>
  )
}
