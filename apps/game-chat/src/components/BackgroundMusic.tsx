import { useBackgroundMusic } from '@/hooks/use-background-music';
import { useEffect } from 'react';

interface BackgroundMusicProps {
  /**
   * Path to the audio file
   */
  src: string;
  /**
   * Whether the audio should play
   */
  play?: boolean;
  /**
   * Whether the audio should loop
   */
  loop?: boolean;
  /**
   * Volume of the audio (0-1)
   */
  volume?: number;
  /**
   * Whether to fade in when starting playback
   */
  fadeIn?: boolean;
  /**
   * Whether to fade out when stopping playback
   */
  fadeOut?: boolean;
  /**
   * Duration of fade in/out in milliseconds
   */
  fadeDuration?: number;
}

/**
 * Component for playing background music
 */
export function BackgroundMusic({
  src,
  play = true,
  loop = true,
  volume = 0.3,
  fadeIn = true,
  fadeOut = true,
  fadeDuration = 1000,
}: BackgroundMusicProps) {
  const { isLoaded, isPlaying, play: startPlay, pause, fadeIn: doFadeIn, fadeOut: doFadeOut } = useBackgroundMusic({
    src,
    loop,
    volume,
    autoPlay: false, // We'll handle playback manually
    preload: true,
  });

  // Handle play/pause based on the play prop
  useEffect(() => {
    if (play && isLoaded && !isPlaying) {
      if (fadeIn) {
        doFadeIn(fadeDuration, volume);
      } else {
        startPlay();
      }
    } else if (!play && isPlaying) {
      if (fadeOut) {
        doFadeOut(fadeDuration);
      } else {
        pause();
      }
    }
  }, [play, isLoaded, isPlaying, startPlay, pause, fadeIn, fadeOut, doFadeIn, doFadeOut, fadeDuration, volume]);

  // This component doesn't render anything
  return null;
}
