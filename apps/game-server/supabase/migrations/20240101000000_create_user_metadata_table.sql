-- Create user_metadata table
CREATE TABLE IF NOT EXISTS public.user_metadata (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  sui_address TEXT,
  is_zk_login B<PERSON><PERSON>EAN DEFAULT FALSE,
  display_name TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Enable RLS
ALTER TABLE public.user_metadata ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own metadata"
  ON public.user_metadata
  FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can update their own metadata"
  ON public.user_metadata
  FOR UPDATE
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own metadata"
  ON public.user_metadata
  FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- Create function to handle user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.user_metadata (user_id)
  VALUES (NEW.id);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger for new user creation
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_new_user();

-- Create function to update updated_at on metadata update
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for updated_at
DROP TRIGGER IF EXISTS update_user_metadata_updated_at ON public.user_metadata;
CREATE TRIGGER update_user_metadata_updated_at
  BEFORE UPDATE ON public.user_metadata
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();
