# @generated by Move, please check-in and do not edit manually.

[move]
version = 3
manifest_digest = "8DE590D66600DB7A6E3423B36C4D0A935745546567E668DBB4D19AD765153A21"
deps_digest = "F8BBB0CCB2491CA29A3DF03D6F92277A4F3574266507ACD77214D37ECA3F3082"
dependencies = [
  { id = "Sui", name = "Sui" },
]

[[move.package]]
id = "MoveStdlib"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/testnet", subdir = "crates/sui-framework/packages/move-stdlib" }

[[move.package]]
id = "Sui"
source = { git = "https://github.com/MystenLabs/sui.git", rev = "framework/testnet", subdir = "crates/sui-framework/packages/sui-framework" }

dependencies = [
  { id = "MoveStdlib", name = "MoveStdlib" },
]

[move.toolchain-version]
compiler-version = "1.46.2"
edition = "2024"
flavor = "sui"

[env]

[env.testnet]
chain-id = "4c78adac"
original-published-id = "0x34ee97b25edbca9f58fac65cf38c2256060484cd1d345e863b274bd7cc95743d"
latest-published-id = "0x34ee97b25edbca9f58fac65cf38c2256060484cd1d345e863b274bd7cc95743d"
published-version = "1"
