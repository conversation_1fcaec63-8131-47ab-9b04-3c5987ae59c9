lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@mysten/sui':
    specifier: 1.25.0
    version: 1.25.0(typescript@5.8.2)
  ws:
    specifier: 8.16.0
    version: 8.16.0

packages:

  /@0no-co/graphql.web@1.1.2(graphql@16.10.0):
    resolution: {integrity: sha512-N2NGsU5FLBhT8NZ+3l2YrzZSHITjNXNuDhC4iDiikv0IujaJ0Xc6xIxQZ/Ek3Cb+rgPjnLHYyJm11tInuJn+cw==}
    peerDependencies:
      graphql: ^14.0.0 || ^15.0.0 || ^16.0.0
    peerDependenciesMeta:
      graphql:
        optional: true
    dependencies:
      graphql: 16.10.0
    dev: false

  /@0no-co/graphqlsp@1.12.16(graphql@16.10.0)(typescript@5.8.2):
    resolution: {integrity: sha512-B5pyYVH93Etv7xjT6IfB7QtMBdaaC07yjbhN6v8H7KgFStMkPvi+oWYBTibMFRMY89qwc9H8YixXg8SXDVgYWw==}
    peerDependencies:
      graphql: ^15.5.0 || ^16.0.0 || ^17.0.0
      typescript: ^5.0.0
    dependencies:
      '@gql.tada/internal': 1.0.8(graphql@16.10.0)(typescript@5.8.2)
      graphql: 16.10.0
      typescript: 5.8.2
    dev: false

  /@gql.tada/cli-utils@1.6.3(@0no-co/graphqlsp@1.12.16)(graphql@16.10.0)(typescript@5.8.2):
    resolution: {integrity: sha512-jFFSY8OxYeBxdKi58UzeMXG1tdm4FVjXa8WHIi66Gzu9JWtCE6mqom3a8xkmSw+mVaybFW5EN2WXf1WztJVNyQ==}
    peerDependencies:
      '@0no-co/graphqlsp': ^1.12.13
      '@gql.tada/svelte-support': 1.0.1
      '@gql.tada/vue-support': 1.0.1
      graphql: ^15.5.0 || ^16.0.0 || ^17.0.0
      typescript: ^5.0.0
    peerDependenciesMeta:
      '@gql.tada/svelte-support':
        optional: true
      '@gql.tada/vue-support':
        optional: true
    dependencies:
      '@0no-co/graphqlsp': 1.12.16(graphql@16.10.0)(typescript@5.8.2)
      '@gql.tada/internal': 1.0.8(graphql@16.10.0)(typescript@5.8.2)
      graphql: 16.10.0
      typescript: 5.8.2
    dev: false

  /@gql.tada/internal@1.0.8(graphql@16.10.0)(typescript@5.8.2):
    resolution: {integrity: sha512-XYdxJhtHC5WtZfdDqtKjcQ4d7R1s0d1rnlSs3OcBEUbYiPoJJfZU7tWsVXuv047Z6msvmr4ompJ7eLSK5Km57g==}
    peerDependencies:
      graphql: ^15.5.0 || ^16.0.0 || ^17.0.0
      typescript: ^5.0.0
    dependencies:
      '@0no-co/graphql.web': 1.1.2(graphql@16.10.0)
      graphql: 16.10.0
      typescript: 5.8.2
    dev: false

  /@graphql-typed-document-node/core@3.2.0(graphql@16.10.0):
    resolution: {integrity: sha512-mB9oAsNCm9aM3/SOv4YtBMqZbYj10R7dkq8byBqxGY/ncFwhf2oQzMV+LCRlWoDSEBJ3COiR1yeDvMtsoOsuFQ==}
    peerDependencies:
      graphql: ^0.8.0 || ^0.9.0 || ^0.10.0 || ^0.11.0 || ^0.12.0 || ^0.13.0 || ^14.0.0 || ^15.0.0 || ^16.0.0 || ^17.0.0
    dependencies:
      graphql: 16.10.0
    dev: false

  /@mysten/bcs@1.5.0:
    resolution: {integrity: sha512-v39dm5oNfKYMAf2CVI+L0OaJiG9RVXsjqPM4BwTKcHNCZOvr35IIewGtXtWXsI67SQU2TRq8lhQzeibdiC/CNg==}
    dependencies:
      '@scure/base': 1.2.4
    dev: false

  /@mysten/sui@1.25.0(typescript@5.8.2):
    resolution: {integrity: sha512-oyncm8WKJ6RZPltJTcCOgdQbOIg6+7XYekMMhU5NDVYNnIhstRXX0axaSwZTR+kON17T92qcXntCN4AszEpH8w==}
    engines: {node: '>=18'}
    dependencies:
      '@graphql-typed-document-node/core': 3.2.0(graphql@16.10.0)
      '@mysten/bcs': 1.5.0
      '@noble/curves': 1.8.1
      '@noble/hashes': 1.7.1
      '@scure/base': 1.2.4
      '@scure/bip32': 1.6.2
      '@scure/bip39': 1.5.4
      gql.tada: 1.8.10(graphql@16.10.0)(typescript@5.8.2)
      graphql: 16.10.0
      poseidon-lite: 0.2.1
      valibot: 0.36.0
    transitivePeerDependencies:
      - '@gql.tada/svelte-support'
      - '@gql.tada/vue-support'
      - typescript
    dev: false

  /@noble/curves@1.8.1:
    resolution: {integrity: sha512-warwspo+UYUPep0Q+vtdVB4Ugn8GGQj8iyB3gnRWsztmUHTI3S1nhdiWNsPUGL0vud7JlRRk1XEu7Lq1KGTnMQ==}
    engines: {node: ^14.21.3 || >=16}
    dependencies:
      '@noble/hashes': 1.7.1
    dev: false

  /@noble/hashes@1.7.1:
    resolution: {integrity: sha512-B8XBPsn4vT/KJAGqDzbwztd+6Yte3P4V7iafm24bxgDe/mlRuK6xmWPuCNrKt2vDafZ8MfJLlchDG/vYafQEjQ==}
    engines: {node: ^14.21.3 || >=16}
    dev: false

  /@scure/base@1.2.4:
    resolution: {integrity: sha512-5Yy9czTO47mqz+/J8GM6GIId4umdCk1wc1q8rKERQulIoc8VP9pzDcghv10Tl2E7R96ZUx/PhND3ESYUQX8NuQ==}
    dev: false

  /@scure/bip32@1.6.2:
    resolution: {integrity: sha512-t96EPDMbtGgtb7onKKqxRLfE5g05k7uHnHRM2xdE6BP/ZmxaLtPek4J4KfVn/90IQNrU1IOAqMgiDtUdtbe3nw==}
    dependencies:
      '@noble/curves': 1.8.1
      '@noble/hashes': 1.7.1
      '@scure/base': 1.2.4
    dev: false

  /@scure/bip39@1.5.4:
    resolution: {integrity: sha512-TFM4ni0vKvCfBpohoh+/lY05i9gRbSwXWngAsF4CABQxoaOHijxuaZ2R6cStDQ5CHtHO9aGJTr4ksVJASRRyMA==}
    dependencies:
      '@noble/hashes': 1.7.1
      '@scure/base': 1.2.4
    dev: false

  /gql.tada@1.8.10(graphql@16.10.0)(typescript@5.8.2):
    resolution: {integrity: sha512-FrvSxgz838FYVPgZHGOSgbpOjhR+yq44rCzww3oOPJYi0OvBJjAgCiP6LEokZIYND2fUTXzQAyLgcvgw1yNP5A==}
    hasBin: true
    peerDependencies:
      typescript: ^5.0.0
    dependencies:
      '@0no-co/graphql.web': 1.1.2(graphql@16.10.0)
      '@0no-co/graphqlsp': 1.12.16(graphql@16.10.0)(typescript@5.8.2)
      '@gql.tada/cli-utils': 1.6.3(@0no-co/graphqlsp@1.12.16)(graphql@16.10.0)(typescript@5.8.2)
      '@gql.tada/internal': 1.0.8(graphql@16.10.0)(typescript@5.8.2)
      typescript: 5.8.2
    transitivePeerDependencies:
      - '@gql.tada/svelte-support'
      - '@gql.tada/vue-support'
      - graphql
    dev: false

  /graphql@16.10.0:
    resolution: {integrity: sha512-AjqGKbDGUFRKIRCP9tCKiIGHyriz2oHEbPIbEtcSLSs4YjReZOIPQQWek4+6hjw62H9QShXHyaGivGiYVLeYFQ==}
    engines: {node: ^12.22.0 || ^14.16.0 || ^16.0.0 || >=17.0.0}
    dev: false

  /poseidon-lite@0.2.1:
    resolution: {integrity: sha512-xIr+G6HeYfOhCuswdqcFpSX47SPhm0EpisWJ6h7fHlWwaVIvH3dLnejpatrtw6Xc6HaLrpq05y7VRfvDmDGIog==}
    dev: false

  /typescript@5.8.2:
    resolution: {integrity: sha512-aJn6wq13/afZp/jT9QZmwEjDqqvSGp1VT5GVg+f/t6/oVyrgXM6BY1h9BRh/O5p3PlUPAe+WuiEZOmb/49RqoQ==}
    engines: {node: '>=14.17'}
    hasBin: true
    dev: false

  /valibot@0.36.0:
    resolution: {integrity: sha512-CjF1XN4sUce8sBK9TixrDqFM7RwNkuXdJu174/AwmQUB62QbCQADg5lLe8ldBalFgtj1uKj+pKwDJiNo4Mn+eQ==}
    dev: false

  /ws@8.16.0:
    resolution: {integrity: sha512-HS0c//TP7Ina87TfiPUz1rQzMhHrl/SG2guqRcTOIUYD2q8uhUdNHZYJUaQ8aTGPzCh+c6oawMKW35nFl1dxyQ==}
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dev: false
